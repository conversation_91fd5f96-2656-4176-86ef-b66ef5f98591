import { Component, OnInit, OnDestroy } from '@angular/core';
import { Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { AuthService } from '../../../core/services/auth.service';
import { CartService } from '../../../core/services/cart.service';

@Component({
  selector: 'app-header',
  standalone: false,
  templateUrl: './header.html',
  styleUrl: './header.css'
})
export class Header implements OnInit, OnDestroy {
  isMenuOpen = false;
  isAuthenticated = false;
  currentUser: any = null;
  cartItemCount = 0;
  private authSubscription?: Subscription;
  private userSubscription?: Subscription;
  private cartSubscription?: Subscription;

  constructor(
    private authService: AuthService,
    private cartService: CartService,
    private router: Router
  ) {}

  ngOnInit(): void {
    // Subscribe to authentication status
    this.authSubscription = this.authService.isAuthenticated$.subscribe(
      isAuth => this.isAuthenticated = isAuth
    );

    // Subscribe to current user data
    this.userSubscription = this.authService.currentUser$.subscribe(
      user => this.currentUser = user
    );

    // Subscribe to cart updates
    this.cartSubscription = this.cartService.cart$.subscribe(
      cart => this.cartItemCount = cart.total_items
    );
  }

  ngOnDestroy(): void {
    this.authSubscription?.unsubscribe();
    this.userSubscription?.unsubscribe();
    this.cartSubscription?.unsubscribe();
  }

  toggleMenu() {
    this.isMenuOpen = !this.isMenuOpen;
  }

  onLogout(): void {
    this.authService.logout();
  }

  navigateToGames(event: Event): void {
    event.preventDefault();
    if (this.isAuthenticated) {
      this.router.navigate(['/profile'], { fragment: 'catalog' });
    } else {
      this.router.navigate(['/login']);
    }
  }

  navigateToCart(event: Event): void {
    event.preventDefault();
    if (this.isAuthenticated) {
      this.router.navigate(['/profile'], { fragment: 'cart' });
    } else {
      this.router.navigate(['/login']);
    }
  }
}
